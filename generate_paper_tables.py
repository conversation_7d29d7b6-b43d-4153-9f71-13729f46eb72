#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成论文所需的表格格式
基于evaluation_results.json生成LaTeX和Markdown格式的表格
"""

import json
import os
from pathlib import Path

def load_results(json_file="evaluation_results.json"):
    """加载评估结果"""
    if not os.path.exists(json_file):
        print(f"❌ 结果文件不存在: {json_file}")
        return None
    
    with open(json_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    return results

def generate_main_metrics_table(results, format_type="markdown"):
    """生成主要指标表格"""
    
    if format_type == "latex":
        table = """
\\begin{table}[h]
\\centering
\\caption{ICAFusion模型在RGBT-Tiny数据集上的性能评估}
\\label{tab:main_metrics}
\\begin{tabular}{|l|c|l|}
\\hline
\\textbf{指标} & \\textbf{数值} & \\textbf{说明} \\\\
\\hline
mAP & {:.4f} & 平均精度均值 (IoU=0.5:0.95) \\\\
mAP@0.5 & {:.4f} & 平均精度均值 (IoU=0.5) \\\\
mAP@0.75 & {:.4f} & 平均精度均值 (IoU=0.75) \\\\
Precision & {:.4f} & 精确率 \\\\
Recall & {:.4f} & 召回率 \\\\
\\hline
FLOPs & {:.2f}G & 浮点运算数 \\\\
Params & {:.2f}M & 参数量 \\\\
FPS & {:.1f} & 每秒帧数 \\\\
\\hline
\\end{{tabular}}
\\end{{table}}
""".format(
            results['mAP'],
            results['mAP50'],
            results['mAP75'],
            results['precision'],
            results['recall'],
            results['FLOPs']/1e9,
            results['Params']/1e6,
            results['FPS']
        )
    
    else:  # markdown
        table = """
| 指标 | 数值 | 说明 |
|------|------|------|
| mAP | {:.4f} | 平均精度均值 (IoU=0.5:0.95) |
| mAP@0.5 | {:.4f} | 平均精度均值 (IoU=0.5) |
| mAP@0.75 | {:.4f} | 平均精度均值 (IoU=0.75) |
| Precision | {:.4f} | 精确率 |
| Recall | {:.4f} | 召回率 |
| FLOPs | {:.2f}G | 浮点运算数 |
| Params | {:.2f}M | 参数量 |
| FPS | {:.1f} | 每秒帧数 |
""".format(
            results['mAP'],
            results['mAP50'],
            results['mAP75'],
            results['precision'],
            results['recall'],
            results['FLOPs']/1e9,
            results['Params']/1e6,
            results['FPS']
        )
    
    return table

def generate_small_target_table(results, format_type="markdown"):
    """生成小目标指标表格"""
    
    if format_type == "latex":
        table = """
\\begin{table}[h]
\\centering
\\caption{ICAFusion模型的小目标检测性能 (SAfit指标)}
\\label{tab:small_target_metrics}
\\begin{tabular}{|l|c|l|}
\\hline
\\textbf{指标} & \\textbf{数值} & \\textbf{说明} \\\\
\\hline
AP\\_et & {:.4f} & 极小目标AP，面积在 [1², 8²) 像素 \\\\
AP\\_t & {:.4f} & 微小目标AP，面积在 [8², 16²) 像素 \\\\
AP\\_s & {:.4f} & 小目标AP，面积在 [16², 32²) 像素 \\\\
\\hline
\\end{{tabular}}
\\end{{table}}
""".format(
            results['AP_et'],
            results['AP_t'],
            results['AP_s']
        )
    
    else:  # markdown
        table = """
| 指标 | 数值 | 说明 |
|------|------|------|
| AP_et | {:.4f} | 极小目标AP，面积在 [1², 8²) 像素 |
| AP_t | {:.4f} | 微小目标AP，面积在 [8², 16²) 像素 |
| AP_s | {:.4f} | 小目标AP，面积在 [16², 32²) 像素 |
""".format(
            results['AP_et'],
            results['AP_t'],
            results['AP_s']
        )
    
    return table

def generate_comparison_table(results, format_type="markdown"):
    """生成与其他方法的对比表格模板"""
    
    if format_type == "latex":
        table = """
\\begin{table}[h]
\\centering
\\caption{不同方法在RGBT-Tiny数据集上的性能对比}
\\label{tab:comparison}
\\begin{tabular}{|l|c|c|c|c|c|}
\\hline
\\textbf{方法} & \\textbf{mAP} & \\textbf{mAP@0.5} & \\textbf{FLOPs(G)} & \\textbf{Params(M)} & \\textbf{FPS} \\\\
\\hline
YOLOv5 & - & - & - & - & - \\\\
YOLOv8 & - & - & - & - & - \\\\
RT-DETR & - & - & - & - & - \\\\
\\textbf{{ICAFusion (Ours)}} & \\textbf{{{:.4f}}} & \\textbf{{{:.4f}}} & \\textbf{{{:.2f}}} & \\textbf{{{:.2f}}} & \\textbf{{{:.1f}}} \\\\
\\hline
\\end{{tabular}}
\\end{{table}}
""".format(
            results['mAP'],
            results['mAP50'],
            results['FLOPs']/1e9,
            results['Params']/1e6,
            results['FPS']
        )
    
    else:  # markdown
        table = """
| 方法 | mAP | mAP@0.5 | FLOPs(G) | Params(M) | FPS |
|------|-----|---------|----------|-----------|-----|
| YOLOv5 | - | - | - | - | - |
| YOLOv8 | - | - | - | - | - |
| RT-DETR | - | - | - | - | - |
| **ICAFusion (Ours)** | **{:.4f}** | **{:.4f}** | **{:.2f}** | **{:.2f}** | **{:.1f}** |
""".format(
            results['mAP'],
            results['mAP50'],
            results['FLOPs']/1e9,
            results['Params']/1e6,
            results['FPS']
        )
    
    return table

def generate_compact_table(results):
    """生成紧凑的单行表格，适合论文"""
    
    # Markdown格式
    markdown_table = """
| mAP | mAP50 | mAP75 | FLOPs | Params | FPS |
|-----|-------|-------|-------|--------|-----|
| {:.4f} | {:.4f} | {:.4f} | {:.2f}G | {:.2f}M | {:.1f} |
""".format(
        results['mAP'],
        results['mAP50'],
        results['mAP75'],
        results['FLOPs']/1e9,
        results['Params']/1e6,
        results['FPS']
    )
    
    # LaTeX格式
    latex_table = """
\\begin{{table}}[h]
\\centering
\\caption{{ICAFusion模型性能总结}}
\\label{{tab:performance_summary}}
\\begin{{tabular}}{{|c|c|c|c|c|c|}}
\\hline
\\textbf{{mAP}} & \\textbf{{mAP@0.5}} & \\textbf{{mAP@0.75}} & \\textbf{{FLOPs(G)}} & \\textbf{{Params(M)}} & \\textbf{{FPS}} \\\\
\\hline
{:.4f} & {:.4f} & {:.4f} & {:.2f} & {:.2f} & {:.1f} \\\\
\\hline
\\end{{tabular}}
\\end{{table}}
""".format(
        results['mAP'],
        results['mAP50'],
        results['mAP75'],
        results['FLOPs']/1e9,
        results['Params']/1e6,
        results['FPS']
    )
    
    return markdown_table, latex_table

def main():
    print("="*80)
    print("🎯 生成论文表格")
    print("="*80)
    
    # 加载结果
    results = load_results()
    if results is None:
        return
    
    print("✅ 成功加载评估结果")
    print(f"📊 mAP: {results['mAP']:.4f}")
    print(f"📊 mAP@0.5: {results['mAP50']:.4f}")
    print(f"⚡ FLOPs: {results['FLOPs']/1e9:.2f}G")
    print(f"📦 Params: {results['Params']/1e6:.2f}M")
    print(f"🚀 FPS: {results['FPS']:.1f}")
    
    # 生成各种格式的表格
    print("\n" + "="*80)
    print("📋 Markdown格式表格")
    print("="*80)
    
    print("\n🎯 主要指标表格:")
    print(generate_main_metrics_table(results, "markdown"))
    
    print("\n🎯 小目标指标表格:")
    print(generate_small_target_table(results, "markdown"))
    
    print("\n🎯 对比表格模板:")
    print(generate_comparison_table(results, "markdown"))
    
    print("\n🎯 紧凑表格:")
    compact_md, compact_latex = generate_compact_table(results)
    print(compact_md)
    
    # 保存到文件
    with open("paper_tables_markdown.md", "w", encoding="utf-8") as f:
        f.write("# ICAFusion 评估结果表格\n\n")
        f.write("## 主要指标\n")
        f.write(generate_main_metrics_table(results, "markdown"))
        f.write("\n## 小目标指标\n")
        f.write(generate_small_target_table(results, "markdown"))
        f.write("\n## 对比表格模板\n")
        f.write(generate_comparison_table(results, "markdown"))
        f.write("\n## 紧凑表格\n")
        f.write(compact_md)
    
    with open("paper_tables_latex.tex", "w", encoding="utf-8") as f:
        f.write("% ICAFusion 评估结果表格 (LaTeX格式)\n\n")
        f.write("% 主要指标表格\n")
        f.write(generate_main_metrics_table(results, "latex"))
        f.write("\n% 小目标指标表格\n")
        f.write(generate_small_target_table(results, "latex"))
        f.write("\n% 对比表格模板\n")
        f.write(generate_comparison_table(results, "latex"))
        f.write("\n% 紧凑表格\n")
        f.write(compact_latex)
    
    print("\n" + "="*80)
    print("💾 表格已保存到文件:")
    print("  📄 paper_tables_markdown.md")
    print("  📄 paper_tables_latex.tex")
    print("="*80)
    
    print("\n🎉 表格生成完成！")
    print("\n💡 使用建议:")
    print("  1. 复制Markdown表格到README或文档中")
    print("  2. 复制LaTeX表格到论文中")
    print("  3. 根据需要调整表格格式和内容")

if __name__ == "__main__":
    main()
