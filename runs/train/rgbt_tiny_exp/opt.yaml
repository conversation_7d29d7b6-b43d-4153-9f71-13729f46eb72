weights: yolov5m.pt
cfg: ./models/transformer/yolov5m_Transfusion_RGBT-Tiny.yaml
data: ./data/multispectral/RGBT-Tiny.yaml
hyp: data/hyp.scratch.yaml
epochs: 300
batch_size: 4
img_size:
- 640
- 640
rect: false
resume: false
nosave: false
notest: false
noautoanchor: false
evolve: false
bucket: ''
cache_images: false
image_weights: false
device: '0'
multi_scale: false
single_cls: false
adam: false
sync_bn: false
local_rank: -1
workers: 0
project: runs/train
entity: null
name: rgbt_tiny_exp
exist_ok: false
quad: false
linear_lr: false
label_smoothing: 0.0
upload_dataset: false
bbox_interval: -1
save_period: -1
artifact_alias: latest
early_stop: true
patience: 20
es_min_delta: 0.0001
world_size: 1
global_rank: -1
save_dir: runs/train/rgbt_tiny_exp
total_batch_size: 4
